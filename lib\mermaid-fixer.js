/**
 * Mermaid代码AI智能修复工具
 * 通过AI分析和修复Mermaid代码中的各种问题
 */

import { getAIConfig, getSavedPassword, getSelectedModel } from "./config-service";

/**
 * 使用AI自动修复Mermaid代码中的问题
 * @param {string} mermaidCode - 原始Mermaid代码
 * @returns {Promise<{fixedCode: string, error: string|null, suggestions: string[]}>} - 修复结果
 */
export async function autoFixMermaidCode(mermaidCode) {
  if (!mermaidCode || typeof mermaidCode !== 'string') {
    return {
      fixedCode: mermaidCode,
      error: "无效的代码输入",
      suggestions: []
    };
  }

  try {
    // 获取AI配置
    const aiConfig = getAIConfig();
    const accessPassword = getSavedPassword();
    const selectedModel = getSelectedModel();

    const response = await fetch("/api/fix-mermaid", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        mermaidCode,
        aiConfig,
        accessPassword,
        selectedModel
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || "修复代码时出错");
    }

    const data = await response.json();
    return {
      fixedCode: data.fixedCode || mermaidCode,
      error: null,
      suggestions: data.suggestions || []
    };
  } catch (error) {
    console.error("AI修复错误:", error);

    // 如果AI修复失败，回退到基础修复
    const basicFixed = basicFixMermaidCode(mermaidCode);
    return {
      fixedCode: basicFixed,
      error: `AI修复失败，已应用基础修复: ${error.message}`,
      suggestions: ["建议检查网络连接或AI服务配置"]
    };
  }
}

/**
 * 基础修复功能（作为AI修复失败时的备选方案）
 * @param {string} mermaidCode - 原始Mermaid代码
 * @returns {string} - 修复后的代码
 */
function basicFixMermaidCode(mermaidCode) {
  if (!mermaidCode || typeof mermaidCode !== 'string') {
    return mermaidCode;
  }

  let fixedCode = mermaidCode;

  // 1. 处理特殊字符 - 用引号包裹包含特殊字符的节点文本
  fixedCode = fixSpecialCharacters(fixedCode);

  // 2. 修复常见的语法错误
  fixedCode = fixCommonSyntaxErrors(fixedCode);

  // 3. 清理多余的空行和空格
  fixedCode = cleanupWhitespace(fixedCode);

  return fixedCode;
}

/**
 * 处理特殊字符，用引号包裹
 * @param {string} code - Mermaid代码
 * @returns {string} - 处理后的代码
 */
function fixSpecialCharacters(code) {
  // 需要用引号包裹的特殊字符模式
  const specialChars = /[()[\]{}<>|&@#$%^*+=~`!?;:,./\\'"]/;
  
  // 匹配节点定义的正则表达式
  // 例如: A[文本内容] 或 A --> B : 文本内容
  const nodePatterns = [
    // 节点定义: A[文本] 或 A(文本) 或 A{文本} 等
    /(\w+)(\[|\(|\{)([^[\](){}]+)(\]|\)|\})/g,
    // 连接线标签: A --> B : 文本
    /(\w+\s*-->\s*\w+\s*:\s*)([^;\n]+)/g,
    // 连接线标签: A --|文本| B
    /(\w+\s*--\|)([^|]+)(\|\s*\w+)/g,
  ];

  let result = code;

  nodePatterns.forEach(pattern => {
    result = result.replace(pattern, (match, ...groups) => {
      // 根据不同的模式处理
      if (groups.length === 4) {
        // 节点定义模式
        const [prefix, openBracket, text, closeBracket] = groups;
        if (specialChars.test(text) && !isAlreadyQuoted(text)) {
          return `${prefix}${openBracket}"${text.trim()}"${closeBracket}`;
        }
      } else if (groups.length === 2) {
        // 连接线标签模式
        const [prefix, text] = groups;
        if (specialChars.test(text) && !isAlreadyQuoted(text)) {
          return `${prefix}"${text.trim()}"`;
        }
      } else if (groups.length === 3) {
        // 连接线中间标签模式
        const [prefix, text, suffix] = groups;
        if (specialChars.test(text) && !isAlreadyQuoted(text)) {
          return `${prefix}"${text.trim()}"${suffix}`;
        }
      }
      return match;
    });
  });

  return result;
}

/**
 * 检查文本是否已经被引号包裹
 * @param {string} text - 要检查的文本
 * @returns {boolean} - 是否已被引号包裹
 */
function isAlreadyQuoted(text) {
  const trimmed = text.trim();
  return (trimmed.startsWith('"') && trimmed.endsWith('"')) ||
         (trimmed.startsWith("'") && trimmed.endsWith("'"));
}

/**
 * 修复常见的语法错误
 * @param {string} code - Mermaid代码
 * @returns {string} - 修复后的代码
 */
function fixCommonSyntaxErrors(code) {
  let result = code;

  // 修复箭头语法
  result = result.replace(/-->/g, ' --> ');
  result = result.replace(/\s+-->\s+/g, ' --> ');

  // 修复节点ID中的非法字符
  result = result.replace(/(\w+)-(\w+)/g, '$1_$2');

  // 修复缺失的分号
  result = result.replace(/(\w+\s*-->\s*\w+(?:\s*:\s*[^;\n]+)?)\s*\n/g, '$1;\n');

  return result;
}

/**
 * 清理多余的空行和空格
 * @param {string} code - Mermaid代码
 * @returns {string} - 清理后的代码
 */
function cleanupWhitespace(code) {
  return code
    .split('\n')
    .map(line => line.trim())
    .filter((line, index, array) => {
      // 保留非空行，以及不连续的空行
      return line !== '' || (index > 0 && array[index - 1] !== '');
    })
    .join('\n')
    .trim();
}

/**
 * 切换图表方向 (TD <-> LR)
 * @param {string} mermaidCode - 原始Mermaid代码
 * @returns {string} - 切换方向后的代码
 */
export function toggleMermaidDirection(mermaidCode) {
  if (!mermaidCode || typeof mermaidCode !== 'string') {
    return mermaidCode;
  }

  let result = mermaidCode;

  // 查找并替换方向定义
  // 匹配 flowchart TD 或 graph TD 等
  result = result.replace(/(flowchart|graph)\s+(TD|TB|LR|RL)/gi, (_, type, direction) => {
    const newDirection = (direction.toUpperCase() === 'TD' || direction.toUpperCase() === 'TB') ? 'LR' : 'TD';
    return `${type} ${newDirection}`;
  });

  // 如果没有找到方向定义，尝试在第一行添加
  if (!result.match(/(flowchart|graph)\s+(TD|TB|LR|RL)/i)) {
    const lines = result.split('\n');
    if (lines.length > 0 && lines[0].trim() !== '') {
      // 检查第一行是否是图表类型声明
      if (lines[0].match(/^(flowchart|graph)$/i)) {
        lines[0] = `${lines[0]} TD`;
      } else if (!lines[0].match(/^(flowchart|graph)/i)) {
        // 如果第一行不是图表声明，添加一个
        lines.unshift('flowchart TD');
      }
      result = lines.join('\n');
    }
  }

  return result;
}
