import { NextResponse } from "next/server";
import OpenAI from "openai";

// 验证访问权限
function verifyAccess(accessPassword) {
  const serverPassword = process.env.ACCESS_PASSWORD;
  return serverPassword && accessPassword === serverPassword;
}

// 获取AI客户端
function getAIClient(aiConfig) {
  // 如果提供了自定义AI配置，使用自定义配置
  if (aiConfig && aiConfig.apiUrl && aiConfig.apiKey) {
    return new OpenAI({
      baseURL: aiConfig.apiUrl,
      apiKey: aiConfig.apiKey,
    });
  }
  
  // 否则使用环境变量配置
  const apiUrl = process.env.AI_API_URL || "https://api.deepseek.com/v1";
  const apiKey = process.env.AI_API_KEY;
  
  if (!apiKey) {
    throw new Error("未配置AI服务密钥");
  }
  
  return new OpenAI({
    baseURL: apiUrl,
    apiKey: api<PERSON><PERSON>,
  });
}

// 获取模型名称
function getModelName(aiConfig, selectedModel) {
  if (aiConfig && aiConfig.modelName) {
    return aiConfig.modelName;
  }
  
  if (selectedModel) {
    return selectedModel;
  }
  
  return process.env.AI_MODEL_NAME || "deepseek-chat";
}

export async function POST(request) {
  try {
    const { mermaidCode, aiConfig, accessPassword, selectedModel } = await request.json();

    if (!mermaidCode) {
      return NextResponse.json(
        { error: "请提供需要修复的Mermaid代码" },
        { status: 400 }
      );
    }

    // 检查访问权限
    const hasPassword = verifyAccess(accessPassword);
    const hasCustomConfig = aiConfig && aiConfig.apiUrl && aiConfig.apiKey;
    
    if (!hasPassword && !hasCustomConfig) {
      return NextResponse.json(
        { error: "需要访问密码或自定义AI配置才能使用修复功能" },
        { status: 403 }
      );
    }

    // 获取AI客户端和模型
    const client = getAIClient(aiConfig);
    const modelName = getModelName(aiConfig, selectedModel);

    // 构建修复提示词
    const systemPrompt = `你是一个专业的Mermaid图表代码修复专家。你的任务是分析和修复Mermaid代码中的各种问题。

请按照以下规则修复代码：

1. **特殊字符处理**：
   - 如果节点文本包含特殊字符（如括号、符号、标点等），请用双引号包裹
   - 特殊字符包括：()[]{}|&@#$%^*+=~!?;:,./\\'\"

2. **语法修复**：
   - 修复箭头语法错误（确保箭头前后有适当空格）
   - 修复节点ID中的非法字符
   - 确保连接线语法正确

3. **保持原意**：
   - 不要改变图表的逻辑结构
   - 保持节点间的连接关系
   - 保持原有的文本内容含义

请返回修复后的代码，并使用\`\`\`mermaid包裹。`;

    const userPrompt = `请修复以下Mermaid代码中的问题：

\`\`\`mermaid
${mermaidCode}
\`\`\`

请返回修复后的代码。`;

    // 调用AI服务
    const completion = await client.chat.completions.create({
      model: modelName,
      messages: [
        { role: "system", content: systemPrompt },
        { role: "user", content: userPrompt }
      ],
      temperature: 0.1, // 使用较低的温度以获得更一致的结果
      max_tokens: 2000,
    });

    const aiResponse = completion.choices[0]?.message?.content;
    
    if (!aiResponse) {
      throw new Error("AI服务返回空响应");
    }

    // 解析AI响应，提取修复后的代码
    const { fixedCode, suggestions } = parseAIResponse(aiResponse, mermaidCode);

    return NextResponse.json({
      fixedCode,
      suggestions,
      originalCode: mermaidCode
    });

  } catch (error) {
    console.error("修复Mermaid代码时出错:", error);
    
    return NextResponse.json(
      { 
        error: error.message || "修复代码时发生未知错误",
        details: process.env.NODE_ENV === 'development' ? error.stack : undefined
      },
      { status: 500 }
    );
  }
}

/**
 * 解析AI响应，提取修复后的代码和建议
 */
function parseAIResponse(aiResponse, originalCode) {
  try {
    // 尝试提取代码块
    const codeBlockRegex = /```(?:mermaid)?\s*([\s\S]*?)\s*```/;
    const match = aiResponse.match(codeBlockRegex);
    
    let fixedCode = originalCode;
    let suggestions = [];

    if (match && match[1]) {
      fixedCode = match[1].trim();
    } else {
      // 如果没有找到代码块，尝试其他方式提取
      const lines = aiResponse.split('\n');
      const codeLines = lines.filter(line => 
        line.trim() && 
        !line.includes('修复') && 
        !line.includes('问题') &&
        !line.includes('说明') &&
        (line.includes('flowchart') || line.includes('graph') || line.includes('-->') || line.includes('[') || line.includes(']'))
      );
      
      if (codeLines.length > 0) {
        fixedCode = codeLines.join('\n').trim();
      }
    }

    // 提取修复建议
    const suggestionKeywords = ['修复', '问题', '改进', '优化', '建议'];
    const responseLines = aiResponse.split('\n');
    
    suggestions = responseLines
      .filter(line => suggestionKeywords.some(keyword => line.includes(keyword)))
      .map(line => line.trim())
      .filter(line => line.length > 0)
      .slice(0, 3); // 最多保留3条建议

    // 如果没有提取到建议，添加默认建议
    if (suggestions.length === 0) {
      suggestions = ["代码已通过AI分析和优化"];
    }

    return { fixedCode, suggestions };
  } catch (error) {
    console.error("解析AI响应时出错:", error);
    return { 
      fixedCode: originalCode, 
      suggestions: ["AI响应解析失败，请检查代码格式"] 
    };
  }
}
