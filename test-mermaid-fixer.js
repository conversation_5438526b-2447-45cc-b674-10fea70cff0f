// AI Mermaid 修复功能演示脚本
// 注意：这个脚本展示了AI修复功能的概念，实际使用需要在浏览器环境中

// 测试用例 - 包含各种常见问题的Mermaid代码
const testCases = [
  {
    name: "特殊字符问题",
    description: "包含未转义的特殊字符",
    input: `flowchart TD
    A[用户登录] --> B{验证成功?}
    B --> C[跳转到主页面(包含特殊字符@#$)]
    C --> D[显示用户信息&数据]`,
    issues: ["特殊字符未用引号包裹", "可能导致渲染失败"]
  },
  {
    name: "语法错误",
    description: "箭头语法和格式问题",
    input: `flowchart TD
A[开始]-->B{判断条件}
B-->|是|C[执行操作]
B-->|否|D[结束]
C-->D`,
    issues: ["箭头缺少空格", "格式不规范"]
  },
  {
    name: "复杂图表问题",
    description: "多种问题混合",
    input: `graph TD
    A[用户注册(包含特殊字符!@#)] --> B{验证邮箱?}
    B -->|通过| C[创建账户]
    B -->|失败| D[显示错误信息&重试]
    C --> E[发送欢迎邮件(包含链接)]
    D --> A`,
    issues: ["特殊字符问题", "格式不一致", "可能的语法错误"]
  },
  {
    name: "方向切换需求",
    description: "需要调整显示方向",
    input: `flowchart TD
    A --> B --> C --> D --> E`,
    issues: ["纵向布局可能不适合长流程"]
  }
];

console.log("🤖 AI Mermaid 修复功能演示\n");
console.log("=".repeat(50));

console.log("\n📋 功能说明:");
console.log("1. 🧠 AI智能分析：使用AI模型分析Mermaid代码问题");
console.log("2. 🔧 自动修复：智能修复特殊字符、语法错误等问题");
console.log("3. 💡 修复建议：提供详细的修复说明和建议");
console.log("4. 🔄 方向切换：智能切换图表显示方向");
console.log("5. 🛡️ 安全回退：AI失败时使用基础规则修复");

console.log("\n🧪 测试用例展示:");
console.log("=".repeat(50));

testCases.forEach((testCase, index) => {
  console.log(`\n${index + 1}. ${testCase.name}`);
  console.log(`   描述: ${testCase.description}`);
  console.log(`   问题: ${testCase.issues.join(", ")}`);
  console.log("\n   原始代码:");
  console.log("   ```mermaid");
  console.log(testCase.input.split('\n').map(line => `   ${line}`).join('\n'));
  console.log("   ```");
  console.log("\n   AI修复过程:");
  console.log("   ⏳ 发送代码到AI服务...");
  console.log("   🤖 AI分析代码结构和问题...");
  console.log("   🔧 应用智能修复规则...");
  console.log("   ✅ 返回修复后的代码和建议");
  console.log("\n" + "-".repeat(40));
});

console.log("\n🎯 AI修复提示词示例:");
console.log("=".repeat(50));
console.log(`
系统提示词：
"你是一个专业的Mermaid图表代码修复专家。请分析并修复以下问题：
1. 特殊字符处理：用双引号包裹含特殊字符的文本
2. 语法修复：修复箭头、连接线等语法错误
3. 格式优化：统一缩进和空行
4. 保持原意：不改变图表逻辑结构"

用户输入：
"请修复以下Mermaid代码中的问题：[用户的代码]"

AI输出：
- 修复后的完整代码
- 详细的修复说明
- 改进建议
`);

console.log("\n🚀 使用方法:");
console.log("=".repeat(50));
console.log("1. 在编辑器中点击 '🪄 AI修复' 按钮");
console.log("2. 或在主界面工具栏点击 '🪄 AI修复' 按钮");
console.log("3. AI会自动分析并修复代码问题");
console.log("4. 查看修复结果和建议");
console.log("5. 如需要，可点击 '🔄 切换方向' 调整布局");

console.log("\n⚙️ 配置要求:");
console.log("=".repeat(50));
console.log("- 需要访问密码或自定义AI配置");
console.log("- 支持OpenAI兼容的AI服务");
console.log("- 推荐使用DeepSeek、GPT等模型");

console.log("\n🎉 优势特点:");
console.log("=".repeat(50));
console.log("✨ 智能化：AI理解上下文，提供精准修复");
console.log("🔒 可靠性：AI失败时自动回退到基础修复");
console.log("📝 详细反馈：提供修复说明和改进建议");
console.log("🎨 用户友好：简单点击即可完成复杂修复");
console.log("🔧 灵活配置：支持多种AI服务和模型");

console.log("\n" + "=".repeat(50));
console.log("💡 提示：在实际使用中，请在浏览器中体验完整的AI修复功能！");
