{"name": "smart-mermaid", "version": "1.1.1", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@codemirror/basic-setup": "^0.20.0", "@codemirror/language": "^6.11.0", "@codemirror/state": "^6.5.2", "@codemirror/view": "^6.37.0", "@excalidraw/excalidraw": "^0.18.0", "@excalidraw/mermaid-to-excalidraw": "^1.1.2", "@hookform/resolvers": "^5.0.1", "@lezer/highlight": "^1.2.1", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "codemirror": "^6.0.1", "codemirror-lang-mermaid": "^0.5.0", "docx": "^9.5.0", "lucide-react": "^0.511.0", "mammoth": "^1.9.0", "mermaid": "^11.6.0", "next": "15.3.2", "next-themes": "^0.4.6", "openai": "^4.100.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.56.4", "sonner": "^2.0.3", "tailwind-merge": "^3.3.0", "tailwindcss-animate": "^1.0.7", "zod": "^3.25.13"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "eslint": "^9", "eslint-config-next": "15.3.2", "tailwindcss": "^4", "tw-animate-css": "^1.3.0"}}